using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;

namespace Game.Views.UI.Screens.Menu
{
    public class MenuScreen : GameScreen
    {
        [SerializeField] private XRSimpleGrabInteractable grabInteractable;
        [SerializeField] private List<Panel> panels;

        private Panel previousPanel;
        private readonly ISubject<(bool, bool)> onGrabStateUpdated = new Subject<(bool, bool)>();

        public MenuPanel MenuPanel { get; private set; }
        public ModerationPanel ModerationPanel { get; private set; }
        public ModerationSentencePanel ModerationSentencePanel { get; private set; }
        public SettingsPanel SettingsPanel { get; private set; }
        public ModalPanel ModalPanel { get; private set; }
        public LoadingPanel LoadingPanel { get; private set; }
        public bool IsGrabbed => grabInteractable.isSelected;
        public IObservable<(bool isGrabbed, bool isLeftHand)> OnGrabStateUpdated => onGrabStateUpdated;

        private void Awake()
        {
            MenuPanel = GetPanel<MenuPanel>();
            ModalPanel = GetPanel<ModalPanel>();
            LoadingPanel = GetPanel<LoadingPanel>();
            ModerationPanel = GetPanel<ModerationPanel>();
            ModerationSentencePanel = GetPanel<ModerationSentencePanel>();
            SettingsPanel = GetPanel<SettingsPanel>();

            foreach (var panel in panels)
            {
                switch (panel)
                {
                    case SettingsPanel settingsPanel:
                        settingsPanel.OnClose.Subscribe(_ => Hide()).AddTo(destroyCancellationToken);
                        settingsPanel.OnBack.Subscribe(_ => OpenPanel<MenuPanel>()).AddTo(destroyCancellationToken);
                        break;
                    case ModerationSentencePanel moderationSentencePanel:
                        moderationSentencePanel.OnClose.Subscribe(_ => Hide()).AddTo(destroyCancellationToken);
                        moderationSentencePanel.OnBack.Subscribe(_ => OpenPanel<ModerationPanel>()).AddTo(destroyCancellationToken);
                        break;
                    case MenuScreenPanelBase menuScreenPanel:
                        menuScreenPanel.OnClose.Subscribe(_ => Hide()).AddTo(destroyCancellationToken);
                        menuScreenPanel.OnBack.Subscribe(_ => OpenPanel<MenuPanel>()).AddTo(destroyCancellationToken);
                        break;
                    case MenuPanel menuPanel:
                        menuPanel.OnClose.Subscribe(_ => Hide()).AddTo(destroyCancellationToken);
                        break;
                }
            }

            grabInteractable.selectEntered.AddListener(HandleSelectEntered);
            grabInteractable.selectExited.AddListener(HandleSelectExited);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            SetActiveColliders(true);
        }

        private void OnDestroy()
        {
            grabInteractable.selectEntered.RemoveListener(HandleSelectEntered);
            grabInteractable.selectExited.RemoveListener(HandleSelectExited);
        }

        public T OpenPanel<T>() where T : Panel
        {
            T targetPanel = null;

            panels.ForEach(panel =>
            {
                if (panel.gameObject.activeSelf && panel is not Modules.UI.ModalPanel && panel is not Modules.UI.LoadingPanel)
                {
                    previousPanel = panel;
                }

                if (panel is T currentPanel)
                {
                    panel.Show();
                    targetPanel = currentPanel;
                }
                else
                {
                    panel.Hide();
                }

                panel.SetActive(panel is T);
            });

            return targetPanel;
        }

        public void OpenPreviousPanel()
        {
            if (previousPanel == null)
            {
                return;
            }

            panels.ForEach(panel => panel.Hide());
            previousPanel.Show();
        }

        public T GetPanel<T>() where T : Panel
        {
            return panels.Find(x => x is T) as T;
        }

        public void SetScale(float scale)
        {
            transform.localScale = Vector3.one * scale;
        }

        private void HandleGrabState(bool isGrabbed)
        {
            SetActiveColliders(!isGrabbed);
        }

        private void HandleSelectEntered(SelectEnterEventArgs args)
        {
            SetActiveColliders(false);
            onGrabStateUpdated.OnNext((true, args.interactorObject.IsLeftHand()));
        }

        private void HandleSelectExited(SelectExitEventArgs args)
        {
            SetActiveColliders(true);
            onGrabStateUpdated.OnNext((false, args.interactorObject.IsLeftHand()));
        }

        private void SetActiveColliders(bool isActive)
        {
            grabInteractable.colliders.ForEach(collider => collider.enabled = isActive);
        }
    }
}