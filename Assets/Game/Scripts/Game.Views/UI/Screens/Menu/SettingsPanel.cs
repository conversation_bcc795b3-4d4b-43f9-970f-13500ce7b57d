using System;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI.Screens.Menu
{
    public class SettingsPanel : MenuScreenPanelBase
    {
        [SerializeField] private ButtonWidget scaleUpWidget;
        [SerializeField] private ButtonWidget scaleDownWidget;

        private readonly ISubject<Unit> onScaleUp = new Subject<Unit>();
        private readonly ISubject<Unit> onScaleDown = new Subject<Unit>();

        public IObservable<Unit> OnScaleUp => onScaleUp;
        public IObservable<Unit> OnScaleDown => onScaleDown;

        protected override void Awake()
        {
            base.Awake();
            
            scaleUpWidget.OnClicked.Subscribe(onScaleUp.OnNext).AddTo(destroyCancellationToken);
            scaleDownWidget.OnClicked.Subscribe(onScaleDown.OnNext).AddTo(destroyCancellationToken);
        }

        public void SetActiveScaleUpButton(bool isActive)
        {
            scaleUpWidget.SetActive(isActive);
        }

        public void SetActiveScaleDownButton(bool isActive)
        {
            scaleDownWidget.SetActive(isActive);
        }
    }
}
