using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Models;
using Game.Views.Players;
using Game.Views.UI.Screens.Menu;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class SettingsPanelController : ControllerBase
    {
        private const float MinPlayerScale = 0.5f;
        private const float MaxPlayerScale = 5.0f;
        private const float ScaleIncrement = 0.5f;

        private MenuScreen menuScreen;
        private SettingsPanel settingsPanel;
        private PlayersModel playersModel;
        private GameModel gameModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;

        [Inject]
        private void Construct(
            IScreenManager screenManager,
            PlayersModel playersModel,
            GameModel gameModel)
        {
            this.playersModel = playersModel;
            this.gameModel = gameModel;
            
            menuScreen = screenManager.GetScreen<MenuScreen>(true);
            settingsPanel = menuScreen.GetPanel<SettingsPanel>();

            var menuPanel = menuScreen.GetPanel<MenuPanel>();
            menuPanel.OnOpenSettings.Subscribe(_ => HandleOpenSettings()).AddTo(DisposeCancellationToken);

            settingsPanel.OnScaleUp.Subscribe(_ => HandleScaleUp()).AddTo(DisposeCancellationToken);
            settingsPanel.OnScaleDown.Subscribe(_ => HandleScaleDown()).AddTo(DisposeCancellationToken);

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayerChanged).AddTo(DisposeCancellationToken);
        }

        private void HandleOpenSettings()
        {
            menuScreen.OpenPanel<SettingsPanel>();
            UpdateScaleButtons();
        }

        private void HandleScaleUp()
        {
            if (LocalPlayer == null) return;

            var currentScale = LocalPlayer.Scale.Value;
            var newScale = currentScale + ScaleIncrement;
            
            if (newScale <= MaxPlayerScale)
            {
                LocalPlayer.SetScale(newScale);
                UpdateScaleButtons();
            }
        }

        private void HandleScaleDown()
        {
            if (LocalPlayer == null) return;

            var currentScale = LocalPlayer.Scale.Value;
            var newScale = currentScale - ScaleIncrement;
            
            if (newScale >= MinPlayerScale)
            {
                LocalPlayer.SetScale(newScale);
                UpdateScaleButtons();
            }
        }

        private void HandleLocalPlayerChanged(PlayerActor player)
        {
            if (player != null)
            {
                player.Scale.Subscribe(_ => UpdateScaleButtons()).AddTo(player);
            }
        }

        private void UpdateScaleButtons()
        {
            if (LocalPlayer == null) return;

            var currentScale = LocalPlayer.Scale.Value;
            settingsPanel.SetActiveScaleUpButton(currentScale < MaxPlayerScale);
            settingsPanel.SetActiveScaleDownButton(currentScale > MinPlayerScale);
        }
    }
}
